<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductInResource\Pages;
use App\Filament\Resources\ProductInResource\RelationManagers;
use App\Models\ProductIn;
use App\Models\ProductInStatus;
use App\Models\Employee;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Support\RawJs;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\DatePicker;
use App\Models\Supplier;
use App\Filament\Components\CompressedFileUpload;

class ProductInResource extends Resource
{
    protected static ?string $model = ProductIn::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-down-tray';

    protected static ?string $modelLabel = 'Produk Masuk';

    protected static ?string $pluralModelLabel = 'Produk Masuk';

    protected static ?string $navigationGroup = 'Transaksi';

    public static function canAccess(): bool
    {
        return auth()->check();
    }

    public static function getNavigationBadge(): ?string
    {
        $user = auth()->user();
        if (!$user) return null;

        $count = 0;

        if ($user->isStoreManager()) {
            $count = static::getModel()::where('status', ProductInStatus::WAITING_STORE_MANAGER_APPROVAL)->count();
        } elseif ($user->isOwner()) {
            $count = static::getModel()::where('status', ProductInStatus::WAITING_OWNER_APPROVAL)->count();
        }

        return $count > 0 ? (string) $count : null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function canEdit(Model $record): bool
    {
        $user = auth()->user();
        if (!$user) return false;

        $canEditStatus = in_array($record->status, [
            ProductInStatus::WAITING_STORE_MANAGER_APPROVAL,
            ProductInStatus::WAITING_OWNER_APPROVAL
        ]);

        // Admin packing hanya bisa edit record milik sendiri yang masih waiting store manager
        if ($user->isPackingAdmin()) {
            return $record->user_id === $user->id &&
                   $record->status === ProductInStatus::WAITING_STORE_MANAGER_APPROVAL;
        }

        // Store manager dan owner bisa edit record yang masih pending
        if ($user->hasFullAccess()) {
            return $canEditStatus;
        }

        return false;
    }

    public static function canDelete(Model $record): bool
    {
        $user = auth()->user();
        if (!$user) return false;

        // Hanya bisa delete jika masih waiting store manager approval
        if ($record->status !== ProductInStatus::WAITING_STORE_MANAGER_APPROVAL) {
            return false;
        }

        // Admin packing hanya bisa delete record milik sendiri
        if ($user->isPackingAdmin()) {
            return $record->user_id === $user->id;
        }

        // Store manager dan owner bisa delete
        return $user->hasFullAccess();
    }

    public static function form(Form $form): Form
    {
        $user = auth()->user();
        $canViewPricing = $user?->canViewPricing() ?? false;

        return $form
            ->schema([
                Forms\Components\DateTimePicker::make('date_in')
                    ->label('Tanggal & Waktu Masuk')
                    ->required()
                    ->default(now())
                    ->seconds(false),
                Forms\Components\Select::make('supplier_id')
                    ->label('Pemasok')
                    ->relationship('supplier', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\Textarea::make('note')
                    ->label('Catatan')
                    ->columnSpanFull(),
                CompressedFileUpload::evidencePhotos('evidence_photos')
                    ->directory('product-in-evidence'),
                Forms\Components\Repeater::make('items')
                    ->label('Item Produk')
                    // Tidak menggunakan relationship untuk create mode agar bisa handle manual
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->label('Produk')
                            ->options(function () {
                                return \App\Models\Product::all()->mapWithKeys(function ($product) {
                                    $label = $product->name;
                                    if ($product->isPackage()) {
                                        $label = "[PAKET] " . $label;
                                    }
                                    return [$product->id => $label];
                                });
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function (Forms\Components\Select $component, $state, Forms\Set $set, Forms\Get $get) use ($canViewPricing) {
                                if ($state) {
                                    $product = \App\Models\Product::find($state);
                                    if ($product) {
                                        if ($canViewPricing && ($get('use_default_price') ?? true)) {
                                            // Untuk kepala toko/owner: set default price jika ada, gunakan 0 jika tidak ada
                                            $defaultPrice = $product->default_purchase_price ?? 0;
                                            $set('purchase_price', number_format($defaultPrice, 0, ',', '.'));
                                        } elseif (!$canViewPricing) {
                                            // Untuk admin packing: set default price jika ada, gunakan 0 jika tidak ada
                                            $defaultPrice = $product->default_purchase_price ?? 0;
                                            $set('purchase_price', $defaultPrice);
                                        }
                                    }
                                }
                            }),
                        Forms\Components\DatePicker::make('expiration_date')
                            ->label('Tanggal Kedaluwarsa')
                            ->required(),
                        Forms\Components\TextInput::make('quantity')
                            ->label('Jumlah')
                            ->numeric()
                            ->required()
                            ->minValue(1),
                        Forms\Components\Checkbox::make('use_default_price')
                            ->label('Gunakan Harga Default Beli')
                            ->default(true)
                            ->live()
                            ->dehydrated(false)
                            ->visible($canViewPricing)
                            ->afterStateUpdated(function (Forms\Components\Checkbox $component, $state, Forms\Get $get, Forms\Set $set) {
                                $productId = $get('product_id');
                                if ($productId) {
                                    $product = \App\Models\Product::find($productId);
                                    if ($product) {
                                        if ($state) {
                                            // Checkbox true: gunakan default price jika ada, gunakan 0 jika tidak ada
                                            $defaultPrice = $product->default_purchase_price ?? 0;
                                            $set('purchase_price', number_format($defaultPrice, 0, ',', '.'));
                                        } else {
                                            // Checkbox false: kosongkan field untuk input manual
                                            $set('purchase_price', null);
                                        }
                                    }
                                }
                            }),
                        Forms\Components\TextInput::make('purchase_price')
                            ->label('Harga Beli')
                            ->numeric()
                            ->prefix('Rp')
                            ->mask(RawJs::make(<<<'JS'
                                $money($input, ',', '.', 2)
                            JS))
                            ->stripCharacters('.')
                            ->dehydrateStateUsing(fn ($state) =>
                                is_string($state) ? str_replace(',', '.', str_replace('.', '', $state)) : $state
                            )
                            ->formatStateUsing(fn ($state): ?string => number_format((float)$state, 0, ',', '.'))
                            ->required(fn (Forms\Get $get): bool => $canViewPricing && !($get('use_default_price') ?? true))
                            ->visible($canViewPricing)
                            ->disabled(fn (Forms\Get $get): bool => $canViewPricing ? ($get('use_default_price') ?? true) : true)
                            ->dehydrated(true),
                        // Hidden field untuk admin packing yang akan auto-set dari default price
                        Forms\Components\Hidden::make('purchase_price')
                            ->visible(!$canViewPricing)
                            ->dehydrated(true),
                    ])
                    ->columns(3)
                    ->required()
                    ->minItems(1)
                    ->columnSpanFull()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                $user = auth()->user();
                if ($user && !$user->canViewOthersRecords()) {
                    $query->where('user_id', $user->id);
                }
                return $query;
            })
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('date_in')
                    ->label('Tanggal & Waktu Masuk')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('supplier.name')
                    ->label('Pemasok')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Jumlah Produk')
                    ->counts('items')
                    ->sortable(),
                Tables\Columns\TextColumn::make('items_sum_quantity')
                    ->label('Total Kuantitas')
                    ->sum('items', 'quantity')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ProductInStatus $state): string => $state->getColor())
                    ->formatStateUsing(fn (ProductInStatus $state): string => $state->getLabel())
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Petugas')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('date_in')
                    ->form([
                        DatePicker::make('created_from')->label('Dari Tanggal'),
                        DatePicker::make('created_until')->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_in', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_in', '<=', $date),
                            );
                    }),
                SelectFilter::make('status')
                    ->label('Status')
                    ->options(ProductInStatus::getOptions()),
                    // Default value dihapus karena tab sudah menangani filtering status
                SelectFilter::make('supplier_id')
                    ->label('Pemasok')
                    ->relationship('supplier', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('user_id')
                    ->label('Petugas')
                    ->options(Employee::query()->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (Model $record): bool => static::canEdit($record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Bulk delete dihapus agar tidak bisa hapus massal
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductIns::route('/'),
            'create' => Pages\CreateProductIn::route('/create'),
            'view' => Pages\ViewProductIn::route('/{record}'),
            'edit' => Pages\EditProductIn::route('/{record}/edit'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Produk Masuk')
                    ->schema([
                        TextEntry::make('date_in')
                            ->label('Tanggal & Waktu Masuk')
                            ->dateTime('d M Y H:i'),
                        TextEntry::make('supplier.name')
                            ->label('Pemasok'),
                        TextEntry::make('user.name')
                            ->label('Petugas'),
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (ProductInStatus $state): string => $state->getColor())
                            ->formatStateUsing(fn (ProductInStatus $state): string => $state->getLabel()),
                        TextEntry::make('note')
                            ->label('Catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                Section::make('Foto Bukti')
                    ->schema([
                        ImageEntry::make('evidence_photos')
                            ->label('Foto Bukti')
                            ->disk('public')
                            ->height(200)
                            ->width(300)
                            ->columnSpanFull()
                            ->stacked()
                            ->extraAttributes([
                                'class' => 'lightbox-image-gallery',
                                'style' => 'cursor: pointer;'
                            ])
                            ->visible(fn ($record) => !empty($record->evidence_photos)),
                    ])
                    ->visible(fn ($record) => !empty($record->evidence_photos))
                    ->collapsed()
                    ->collapsible()
                    ->columns(3),
                Section::make('Informasi Persetujuan')
                    ->schema([
                        TextEntry::make('managerApprovedBy.name')
                            ->label('Disetujui Kepala Toko')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('manager_approved_at')
                            ->label('Tanggal Persetujuan Kepala Toko')
                            ->dateTime('d M Y H:i')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('ownerApprovedBy.name')
                            ->label('Disetujui Owner')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('owner_approved_at')
                            ->label('Tanggal Persetujuan Owner')
                            ->dateTime('d M Y H:i')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->columnSpanFull()
                            ->visible(fn (ProductIn $record): bool => $record->isRejected()),
                    ])
                    ->columns(2)
                    ->visible(fn (ProductIn $record): bool =>
                        $record->status !== ProductInStatus::WAITING_STORE_MANAGER_APPROVAL ||
                        auth()->user()?->hasFullAccess()
                    ),
            ]);
    }
}
