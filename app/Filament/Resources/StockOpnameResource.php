<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockOpnameResource\Pages;
use App\Filament\Resources\StockOpnameResource\RelationManagers;
use App\Models\Employee;
use App\Models\Product;
use App\Models\StockBatch;
use App\Models\StockOpname;
use App\Models\StockOpnameItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\DatePicker;

class StockOpnameResource extends Resource
{
    protected static ?string $model = StockOpname::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationGroup = 'Manajemen Stok';

    protected static ?string $modelLabel = 'Stok Opname';

    protected static ?string $pluralModelLabel = 'Stok Opname';

    public static function canAccess(): bool
    {
        return auth()->check();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\DatePicker::make('date_opname')
                    ->label('Tanggal Opname')
                    ->required()
                    ->default(now()),
                Forms\Components\Textarea::make('note')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn() => \Illuminate\Support\Facades\Auth::id()),

                Forms\Components\Repeater::make('items')
                    ->label('Item Opname')
                    ->relationship()
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->label('Produk')
                            ->options(Product::singles()->pluck('name', 'id'))
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('batch_id', null))
                            ->searchable()
                            ->helperText('Hanya produk satuan yang dapat dipilih untuk stok opname'),

                        Forms\Components\Select::make('batch_id')
                            ->label('Batch')
                            ->options(function (callable $get) {
                                $productId = $get('product_id');
                                if (!$productId) {
                                    return [];
                                }

                                return StockBatch::where('product_id', $productId)
                                    ->get()
                                    ->mapWithKeys(function ($batch) {
                                        // Format: Exp: 27 Nov 2025 [Rp 35.000]
                                        $expDate = $batch->expiration_date->format('d M Y');
                                        $price = 'Rp ' . number_format($batch->purchase_price, 0, ',', '.');

                                        return [
                                            $batch->id => "Exp: {$expDate} [{$price}]"
                                        ];
                                    });
                            })
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state) {
                                    $batch = StockBatch::find($state);
                                    if ($batch) {
                                        // Set system quantities for all conditions
                                        $set('system_qty_good', $batch->quantity);
                                        $set('system_qty_bad', $batch->bad_quantity);
                                        $set('system_qty_unusable', $batch->unusable_quantity);
                                    }
                                }
                            })
                            ->searchable(),

                        // Stok Kondisi Baik
                        Forms\Components\Fieldset::make('Kondisi Baik')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('system_qty_good')
                                            ->label('Stok Sistem')
                                            ->readonly()
                                            ->numeric()
                                            ->default(0),
                                        Forms\Components\TextInput::make('actual_qty_good')
                                            ->label('Stok Aktual')
                                            ->required()
                                            ->numeric()
                                            ->minValue(0)
                                            ->default(0),
                                    ])
                            ]),

                        // Stok Kondisi Buruk
                        Forms\Components\Fieldset::make('Kondisi Buruk')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('system_qty_bad')
                                            ->label('Stok Sistem')
                                            ->readonly()
                                            ->numeric()
                                            ->default(0),
                                        Forms\Components\TextInput::make('actual_qty_bad')
                                            ->label('Stok Aktual')
                                            ->required()
                                            ->numeric()
                                            ->minValue(0)
                                            ->default(0),
                                    ])
                            ]),

                        // Stok Kondisi Tidak Layak Pakai
                        Forms\Components\Fieldset::make('Kondisi Tidak Layak Pakai')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('system_qty_unusable')
                                            ->label('Stok Sistem')
                                            ->readonly()
                                            ->numeric()
                                            ->default(0),
                                        Forms\Components\TextInput::make('actual_qty_unusable')
                                            ->label('Stok Aktual')
                                            ->required()
                                            ->numeric()
                                            ->minValue(0)
                                            ->default(0),
                                    ])
                            ]),


                    ])
                    ->columns(2)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                $user = auth()->user();
                if ($user && !$user->canViewOthersRecords()) {
                    $query->where('user_id', $user->id);
                }
                return $query;
            })
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('date_opname')
                    ->label('Tanggal Opname')
                    ->date('d M Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('note')
                    ->label('Catatan')
                    ->limit(50),
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Jumlah Item')
                    ->counts('items')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Petugas')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('date_opname')
                    ->form([
                        DatePicker::make('created_from')->label('Dari Tanggal'),
                        DatePicker::make('created_until')->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_opname', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_opname', '<=', $date),
                            );
                    }),
                SelectFilter::make('user_id')
                    ->label('Petugas')
                    ->options(Employee::query()->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockOpnames::route('/'),
            'create' => Pages\CreateStockOpname::route('/create'),
            'view' => Pages\ViewStockOpname::route('/{record}'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('date_opname')
                            ->label('Tanggal Opname')
                            ->date('d M Y'),
                        TextEntry::make('user.name')
                            ->label('Petugas'),
                        TextEntry::make('note')
                            ->label('Catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }
}
