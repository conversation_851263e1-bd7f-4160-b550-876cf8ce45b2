<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductReturnResource\Pages;
use App\Filament\Resources\ProductReturnResource\RelationManagers;
use App\Models\ProductReturn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\DatePicker;
use App\Models\SalesChannel;
use App\Models\Employee;

class ProductReturnResource extends Resource
{
    protected static ?string $model = ProductReturn::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-uturn-left';

    protected static ?string $modelLabel = 'Retur Produk';

    protected static ?string $pluralModelLabel = 'Retur Produk';

    protected static ?string $navigationGroup = 'Transaksi';

    public static function canAccess(): bool
    {
        return auth()->check();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\DateTimePicker::make('date_ret')
                    ->label('Tanggal & Waktu Retur')
                    ->required()
                    ->default(now())
                    ->seconds(false),
                Forms\Components\Select::make('channel_id')
                    ->label('Saluran Penjualan')
                    ->relationship('salesChannel', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\Textarea::make('note')
                    ->label('Catatan')
                    ->columnSpanFull(),
                Forms\Components\Repeater::make('items')
                    ->label('Item Produk')
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->label('Produk')
                            ->options(function () {
                                return \App\Models\Product::orderBy('name')->get()->mapWithKeys(function ($product) {
                                    $label = $product->name;
                                    if ($product->isPackage()) {
                                        $label = "[PAKET] " . $label;
                                    }
                                    return [$product->id => $label];
                                });
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                // Reset fields ketika produk berubah
                                $set('expiration_date', null);
                                $set('component_expiration_dates', []);
                                $set('component_conditions', []);
                                $set('is_package', false);

                                if ($state) {
                                    $product = \App\Models\Product::find($state);
                                    if ($product && $product->isPackage()) {
                                        $set('is_package', true);
                                        // Set default component structure
                                        $packageReturnService = new \App\Services\PackageReturnService();
                                        $componentsInfo = $packageReturnService->getPackageComponentsInfo($state);
                                        $componentDates = [];
                                        $componentConditions = [];
                                        foreach ($componentsInfo as $component) {
                                            // Gunakan unique key: component_id + stock_condition
                                            $uniqueKey = $component['component_id'] . '_' . $component['stock_condition'];
                                            $componentDates[$uniqueKey] = null;
                                            // Set default berdasarkan kondisi asli komponen
                                            $componentConditions[$uniqueKey] = $component['stock_condition'] === 'good' ? 'good' : 'bad';
                                        }
                                        $set('component_expiration_dates', $componentDates);
                                        $set('component_conditions', $componentConditions);
                                    }
                                }
                            }),

                        // Field untuk produk satuan
                        Forms\Components\Select::make('expiration_date')
                            ->label('Tanggal Kadaluarsa')
                            ->required(fn (callable $get) => !$get('is_package'))
                            ->hidden(fn (callable $get) => $get('is_package'))
                            ->options(function (callable $get) {
                                $productId = $get('product_id');
                                if (!$productId) {
                                    return [];
                                }

                                // Ambil semua tanggal kadaluarsa yang pernah ada untuk produk ini
                                // (termasuk yang stoknya 0, karena mungkin ada retur dari produk yang sudah keluar)
                                $batches = \App\Models\StockBatch::where('product_id', $productId)
                                    ->orderBy('expiration_date', 'asc')
                                    ->get();

                                $options = [];
                                foreach ($batches as $batch) {
                                    $dateString = $batch->expiration_date->format('Y-m-d');
                                    $formattedDate = $batch->expiration_date->format('d M Y');

                                    $options[$dateString] = $formattedDate;
                                }

                                return $options;
                            })
                            ->searchable()
                            ->placeholder('Pilih tanggal kadaluarsa...')
                            ->reactive(),

                        Forms\Components\TextInput::make('quantity')
                        ->label('Jumlah')
                        ->numeric()
                        ->required()
                        ->minValue(1),

                        // Field untuk produk paket - komponen details
                        Forms\Components\Fieldset::make('Detail Komponen Paket')
                            ->schema(function (callable $get) {
                                $productId = $get('product_id');
                                if (!$productId) return [];

                                $product = \App\Models\Product::find($productId);
                                if (!$product || !$product->isPackage()) return [];

                                $packageReturnService = new \App\Services\PackageReturnService();
                                $componentsInfo = $packageReturnService->getPackageComponentsInfo($productId);

                                $fields = [];
                                foreach ($componentsInfo as $component) {
                                    $availableDates = collect($component['available_batches'])
                                        ->mapWithKeys(function ($batch) {
                                            $date = $batch['expiration_date'];
                                            $formattedDate = \Carbon\Carbon::parse($date)->format('d M Y');
                                            return [$date => $formattedDate];
                                        })
                                        ->toArray();

                                    // Gunakan unique key: component_id + stock_condition
                                    $uniqueKey = $component['component_id'] . '_' . $component['stock_condition'];

                                    // Group fields untuk setiap komponen
                                    $fieldsetLabel = $component['component_name'] . ' (' . $component['stock_condition_label'] . ')';
                                    $fields[] = Forms\Components\Fieldset::make($fieldsetLabel)
                                        ->schema([
                                            Forms\Components\Select::make("component_expiration_dates.{$uniqueKey}")
                                                ->label('Tanggal Kadaluarsa')
                                                ->options($availableDates)
                                                ->required()
                                                ->searchable()
                                                ->placeholder('Pilih tanggal kadaluarsa...')
                                                ->helperText("Dibutuhkan per paket: {$component['quantity_per_package']}"),

                                            Forms\Components\Select::make("component_conditions.{$uniqueKey}")
                                                ->label('Kondisi Retur')
                                                ->options(function () use ($component) {
                                                    $options = [
                                                        'bad' => 'Buruk',
                                                        'unusable' => 'Tidak Layak Pakai',
                                                    ];

                                                    // Hanya tampilkan opsi "Baik" jika kondisi asli adalah "good"
                                                    if ($component['stock_condition'] === 'good') {
                                                        $options = ['good' => 'Baik'] + $options;
                                                    }

                                                    return $options;
                                                })
                                                ->required()
                                                ->helperText(function () use ($component) {
                                                    if ($component['stock_condition'] === 'bad') {
                                                        return 'Komponen ini memiliki kondisi asli "Buruk". Opsi "Baik" tidak tersedia untuk mencegah upgrade kondisi yang tidak valid.';
                                                    }
                                                    return 'Pilih kondisi retur untuk komponen ini.';
                                                }),


                                        ])
                                        ->columns(2);
                                }

                                return $fields;
                            })
                            ->visible(fn (callable $get) => $get('is_package'))
                            ->columnSpanFull(),

                        // Field kondisi untuk produk satuan
                        Forms\Components\Select::make('condition')
                            ->label('Kondisi Retur')
                            ->options([
                                'good' => 'Baik',
                                'bad' => 'Buruk',
                                'unusable' => 'Tidak Layak Pakai',
                            ])
                            ->required(fn (callable $get) => !$get('is_package'))
                            ->hidden(fn (callable $get) => $get('is_package')),

                        // Hidden fields untuk tracking
                        Forms\Components\Hidden::make('is_package')
                            ->default(false),
                        Forms\Components\Hidden::make('component_expiration_dates')
                            ->default([]),
                        Forms\Components\Hidden::make('component_conditions')
                            ->default([]),
                    ])
                    ->columns(2)
                    ->required()
                    ->minItems(1)
                    ->columnSpanFull()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                $user = auth()->user();
                if ($user && !$user->canViewOthersRecords()) {
                    $query->where('user_id', $user->id);
                }
                return $query;
            })
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('date_ret')
                    ->label('Tanggal & Waktu Retur')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('salesChannel.name')
                    ->label('Saluran Penjualan')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Jumlah Produk')
                    ->counts('items')
                    ->sortable(),
                Tables\Columns\TextColumn::make('items_sum_quantity')
                    ->label('Total Kuantitas')
                    ->sum('items', 'quantity')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Petugas')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('date_ret')
                    ->form([
                        DatePicker::make('created_from')->label('Dari Tanggal'),
                        DatePicker::make('created_until')->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_ret', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_ret', '<=', $date),
                            );
                    }),
                SelectFilter::make('channel_id')
                    ->label('Saluran Penjualan')
                    ->relationship('salesChannel', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('user_id')
                    ->label('Petugas')
                    ->options(Employee::query()->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(), // dihapus agar tidak bisa hapus massal
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductReturns::route('/'),
            'create' => Pages\CreateProductReturn::route('/create'),
            'view' => Pages\ViewProductReturn::route('/{record}'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('date_ret')
                            ->label('Tanggal & Waktu Retur')
                            ->dateTime('d M Y H:i'),
                        TextEntry::make('salesChannel.name')
                            ->label('Saluran Penjualan'),
                        TextEntry::make('user.name')
                            ->label('Petugas'),
                        TextEntry::make('note')
                            ->label('Catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(3),
            ]);
    }
}
